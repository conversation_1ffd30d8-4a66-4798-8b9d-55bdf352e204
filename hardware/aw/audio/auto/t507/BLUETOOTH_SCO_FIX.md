# Bluetooth SCO Audio Fix for T507 Platform

## Problem Description

The system was experiencing Bluetooth SCO (Synchronous Connection-Oriented) audio issues during phone calls. The logs showed:

```
07-29 11:36:04.957  1886  2178 V audio_hw_primary: out_set_parameters: routing=2
07-29 11:36:05.031  1886  6217 D audio_hw_primary: in call mode, not BT SCO, skip out_write
07-29 11:36:05.077  1886  6832 D audio_hw_primary: in call mode, not BT SCO, skip in_read
```

The audio system was skipping audio operations because it detected "in call mode, not BT SCO", even though Bluetooth SCO should have been active.

## Root Cause Analysis

1. **Device Routing Issue**: The audio routing was set to `routing=2` (AUDIO_DEVICE_OUT_SPEAKER) instead of Bluetooth SCO device values:
   - AUDIO_DEVICE_OUT_BLUETOOTH_SCO = 0x10 (16)
   - AUDIO_DEVICE_OUT_BLUETOOTH_SCO_HEADSET = 0x20 (32)
   - AUDIO_DEVICE_OUT_BLUETOOTH_SCO_CARKIT = 0x40 (64)
   - AUDIO_DEVICE_IN_BLUETOOTH_SCO_HEADSET = 0x80000008

2. **Missing Input Parameter Handling**: The `in_set_parameters` function was not implemented properly - it was just returning 0 without processing any routing parameters.

3. **Insufficient Debugging**: The existing logs didn't show device values in hex format, making it difficult to diagnose routing issues.

## Solution Implemented

### 1. Fixed Input Stream Parameter Handling

**File**: `hardware/aw/audio/auto/t507/audio_hw.c`

**Before**:
```c
static int in_set_parameters(struct audio_stream *stream, const char *kvpairs)
{
    UNUSED(stream);
    ALOGV("in_set_parameters: %s", kvpairs);
    return 0;
}
```

**After**:
```c
static int in_set_parameters(struct audio_stream *stream, const char *kvpairs)
{
    struct sunxi_stream_in *in = (struct sunxi_stream_in *)stream;
    struct str_parms *parms;
    char value[128];
    int ret, val = 0;

    parms = str_parms_create_str(kvpairs);

    ALOGV("in_set_parameters: %s", kvpairs);

    ret = str_parms_get_str(parms, AUDIO_PARAMETER_STREAM_ROUTING, value, sizeof(value));
    if (ret >= 0) {
        val = atoi(value);
        in->device = val;
        ALOGD("in_set_parameters: routing=%d (0x%x)", val, val);
        
        // Log if this is a Bluetooth SCO device
        if (val == AUDIO_DEVICE_IN_BLUETOOTH_SCO_HEADSET) {
            ALOGD("in_set_parameters: Bluetooth SCO headset input device set");
        }
    }

    str_parms_destroy(parms);
    return ret;
}
```

### 2. Enhanced Output Stream Parameter Debugging

Added hex values and Bluetooth SCO device detection to `out_set_parameters`.

### 3. Improved Debugging in Audio Read/Write Functions

Enhanced logging in `out_write` and `in_read` functions to show device values in hex format.

## Testing Instructions

### 1. Build the Fixed Audio HAL

```bash
# From Android build root directory
./hardware/aw/audio/auto/t507/rebuild_audio_hal.sh
```

### 2. Deploy to Device

```bash
# Push the new audio HAL
adb push out/target/product/*/vendor/lib/hw/audio.primary.*.so /vendor/lib/hw/
adb push out/target/product/*/vendor/lib64/hw/audio.primary.*.so /vendor/lib64/hw/

# Restart audio service
adb shell stop audioserver
adb shell start audioserver
```

### 3. Test and Monitor

```bash
# Monitor logs during Bluetooth call
adb logcat | grep -E "(audio_hw_primary|blueware|SCO)"
```

### Expected Log Output After Fix

With the fix, you should see logs like:
```
audio_hw_primary: out_set_parameters: routing=32 (0x20)
audio_hw_primary: out_set_parameters: Bluetooth SCO output device set (0x20)
audio_hw_primary: in_set_parameters: routing=********** (0x80000008)
audio_hw_primary: in_set_parameters: Bluetooth SCO headset input device set
audio_hw_primary: Bluetooth SCO call mode, allow out_write (device=0x20)
audio_hw_primary: Bluetooth SCO call mode, allow in_read (device=0x80000008)
```

## Additional Considerations

If the routing is still not being set correctly by the Bluetooth stack, you may need to:

1. Check the Bluetooth audio policy configuration
2. Verify the Bluetooth SCO connection establishment
3. Check if the AudioManager is properly routing audio to Bluetooth SCO

The fix ensures that when the routing IS set correctly, the audio HAL will handle it properly.
