#!/bin/bash

# Test script for Bluetooth SCO audio fix
# This script helps test and debug Bluetooth SCO audio issues

echo "=== Bluetooth SCO Audio Test Script ==="
echo ""

# Function to check if device is connected
check_device() {
    if ! adb devices | grep -q "device$"; then
        echo "Error: No Android device connected via ADB"
        echo "Please connect your device and enable USB debugging"
        exit 1
    fi
    echo "✓ Device connected"
}

# Function to check audio HAL version
check_audio_hal() {
    echo "Checking audio HAL..."
    adb shell ls -la /vendor/lib/hw/audio.primary.*.so 2>/dev/null
    adb shell ls -la /vendor/lib64/hw/audio.primary.*.so 2>/dev/null
}

# Function to monitor logs
monitor_logs() {
    echo ""
    echo "=== Monitoring Audio Logs ==="
    echo "Start a Bluetooth call now and watch for the following:"
    echo "1. Device routing values (should be 0x10, 0x20, 0x40 for output, 0x80000008 for input)"
    echo "2. 'Bluetooth SCO' messages indicating proper detection"
    echo "3. 'allow out_write' and 'allow in_read' messages"
    echo ""
    echo "Press Ctrl+C to stop monitoring"
    echo ""
    
    adb logcat -c  # Clear existing logs
    adb logcat | grep -E "(audio_hw_primary|blueware|SCO|routing=)" --color=always
}

# Function to show current audio routing
show_routing() {
    echo ""
    echo "=== Current Audio Routing ==="
    adb shell dumpsys audio | grep -A 10 -B 10 "routing\|device\|bluetooth"
}

# Function to test Bluetooth status
check_bluetooth() {
    echo ""
    echo "=== Bluetooth Status ==="
    adb shell dumpsys bluetooth_manager | grep -E "(SCO|A2DP|Connected|State)"
}

# Function to show audio devices
show_audio_devices() {
    echo ""
    echo "=== Audio Devices ==="
    adb shell dumpsys audio | grep -A 20 "Audio Devices:"
}

# Main menu
main_menu() {
    while true; do
        echo ""
        echo "=== Bluetooth SCO Test Menu ==="
        echo "1. Check device connection"
        echo "2. Check audio HAL files"
        echo "3. Monitor audio logs (real-time)"
        echo "4. Show current audio routing"
        echo "5. Check Bluetooth status"
        echo "6. Show audio devices"
        echo "7. Restart audio service"
        echo "8. Exit"
        echo ""
        read -p "Select option (1-8): " choice
        
        case $choice in
            1) check_device ;;
            2) check_audio_hal ;;
            3) monitor_logs ;;
            4) show_routing ;;
            5) check_bluetooth ;;
            6) show_audio_devices ;;
            7) 
                echo "Restarting audio service..."
                adb shell stop audioserver
                sleep 2
                adb shell start audioserver
                echo "Audio service restarted"
                ;;
            8) 
                echo "Exiting..."
                exit 0
                ;;
            *) 
                echo "Invalid option. Please select 1-8."
                ;;
        esac
    done
}

# Check if adb is available
if ! command -v adb &> /dev/null; then
    echo "Error: adb command not found"
    echo "Please install Android SDK platform-tools"
    exit 1
fi

# Start main menu
main_menu
