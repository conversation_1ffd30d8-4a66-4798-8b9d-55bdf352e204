#!/bin/bash

# <PERSON><PERSON>t to rebuild the audio HAL after making changes
# This script should be run from the Android build root directory

echo "Rebuilding audio HAL for t507 platform..."

# Clean previous build artifacts
echo "Cleaning previous build artifacts..."
rm -f out/target/product/*/vendor/lib/hw/audio.primary.*.so
rm -f out/target/product/*/vendor/lib64/hw/audio.primary.*.so

# Build the audio HAL module
echo "Building audio HAL module..."
mmm hardware/aw/audio/auto/t507/

if [ $? -eq 0 ]; then
    echo "Build successful!"
    echo ""
    echo "To test the fix:"
    echo "1. Push the new audio HAL to the device:"
    echo "   adb push out/target/product/*/vendor/lib/hw/audio.primary.*.so /vendor/lib/hw/"
    echo "   adb push out/target/product/*/vendor/lib64/hw/audio.primary.*.so /vendor/lib64/hw/"
    echo ""
    echo "2. Restart the audio service:"
    echo "   adb shell stop audioserver"
    echo "   adb shell start audioserver"
    echo ""
    echo "3. Test Bluetooth SCO call and check logs:"
    echo "   adb logcat | grep -E '(audio_hw_primary|blueware|SCO)'"
    echo ""
    echo "Look for the new debug messages showing device routing values."
else
    echo "Build failed! Please check the error messages above."
    exit 1
fi
